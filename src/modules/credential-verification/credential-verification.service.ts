import {
  ConflictException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MailerService } from '@nestjs-modules/mailer';
import { add, isBefore } from 'date-fns';
import { and, eq, lt } from 'drizzle-orm';

import { verifyEmailTemplate } from '@/common/emails/verify-email.email';
import { Forwarded } from '@/common/types';
import { generateId } from '@/common/utils/generateId';
import { generatePassword } from '@/common/utils/generatePassword';
import { hashTokenConsistently } from '@/common/utils/hash';

import { Database } from '../db/db.module';
import { credentialVerifications } from '../db/entities/credential-verification.entity';
import { UsersService } from '../users/users.service';
import { CreateCredentialVerificationDto } from './dto/create-verification-request.dto';
import { CredentialVerificationDto } from './dto/credential-verification.dto';

@Injectable()
export class CredentialVerificationService {
  private readonly logger = new Logger(CredentialVerificationService.name);

  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => UsersService))
    private readonly userService: Forwarded<UsersService>,
    private readonly mailerService: MailerService,
  ) {}

  async create(
    createCredentialVerificationDto: CreateCredentialVerificationDto,
  ) {
    const expiresAt = add(new Date(), { hours: 1 });
    await this.db
      .delete(credentialVerifications)
      .where(
        and(
          eq(
            credentialVerifications.userId,
            createCredentialVerificationDto.userId,
          ),
          eq(
            credentialVerifications.type,
            createCredentialVerificationDto.type,
          ),
        ),
      );

    return this.db
      .insert(credentialVerifications)
      .values({ ...createCredentialVerificationDto, expiresAt });
  }

  async createAndSendEmail(
    createCredentialVerificationDto: Omit<
      Omit<CreateCredentialVerificationDto, 'hashedToken'>,
      'code'
    >,
  ) {
    const verificationToken = generateId();
    const hashedToken = await hashTokenConsistently(verificationToken);
    const code = await this.generateCode(
      createCredentialVerificationDto.userId,
    );

    await this.create({
      ...createCredentialVerificationDto,
      hashedToken,
      code,
    });

    await this.sendEmail(
      createCredentialVerificationDto.contact,
      verificationToken,
    );
  }

  async createAndSendSms(
    createCredentialVerificationDto: Omit<
      Omit<CreateCredentialVerificationDto, 'hashedToken'>,
      'code'
    >,
  ) {
    const verificationToken = generateId();
    const hashedToken = await hashTokenConsistently(verificationToken);
    const code = await this.generateCode(
      createCredentialVerificationDto.userId,
    );

    await this.create({
      ...createCredentialVerificationDto,
      hashedToken,
      code,
    });

    await this.sendSms(createCredentialVerificationDto.contact, code);
  }

  async sendEmail(email: string, verificationToken: string) {
    await this.mailerService
      .sendMail({
        to: email,
        subject: 'Email verification',
        html: verifyEmailTemplate(
          `https://api.sstw-admin.com/credential-verification/verify/${verificationToken}`,
        ),
      })
      .catch((error: unknown) => {
        console.error(error);
        throw new InternalServerErrorException('Failed to send email');
      });
  }

  async sendSms(phoneNumber: string, verificationToken: string) {
    await this.mailerService.sendMail({
      to: phoneNumber,
      subject: 'Phone verification',
      html: `
      <p>Please verify your phone number by clicking the following link:</p>
      <a href="https://auth.sstw.io/verify-phone/${verificationToken}">Verify phone</a>
      `,
    });
  }

  async findByUserId(userId: string) {
    return this.db.query.credentialVerifications.findMany({
      where: (credentialVerifications, { eq }) =>
        eq(credentialVerifications.userId, userId),
    });
  }

  async delete(id: string) {
    return this.db
      .delete(credentialVerifications)
      .where(eq(credentialVerifications.id, id));
  }

  async verify(credentialVerification: CredentialVerificationDto) {
    return this.db.transaction(async (tx) => {
      const verificationRecord =
        await tx.query.credentialVerifications.findFirst({
          where: (cv, { eq, and, gt }) =>
            and(
              eq(cv.id, credentialVerification.id),
              gt(cv.expiresAt, new Date()),
            ),
        });

      if (!verificationRecord) {
        throw new NotFoundException('Verification record not found or expired');
      }

      const user = await tx.query.users.findFirst({
        where: (users, { eq }) => eq(users.id, credentialVerification.userId),
      });

      if (!user) throw new NotFoundException('User not found');

      if (credentialVerification.type === 'email') {
        await this.userService.update(user.id, {
          isEmailVerified: true,
        });
      } else if (credentialVerification.type === 'phone') {
        await this.userService.update(user.id, {
          isPhoneVerified: true,
        });
      }

      await tx
        .delete(credentialVerifications)
        .where(eq(credentialVerifications.id, credentialVerification.id));

      return true;
    });
  }

  async verifyByToken(token: string) {
    const verificationRequest = await this.findByToken(token);
    if (!verificationRequest)
      throw new NotFoundException(
        'No verification request with this token was found',
      );

    return this.verify(verificationRequest);
  }

  async verifyByCode(code: string, userId: string) {
    const verificationRequest = await this.findByCodeAndUser(code, userId);
    if (!verificationRequest)
      throw new NotFoundException(
        'No verification request with this code was found for this user',
      );

    return this.verify(verificationRequest);
  }

  private async findByToken(token: string) {
    const hashedToken = await hashTokenConsistently(token);

    const verificationRequest =
      await this.db.query.credentialVerifications.findFirst({
        where: (credentialVerifications, { eq, and, gt }) =>
          and(
            eq(credentialVerifications.hashedToken, hashedToken),
            gt(credentialVerifications.expiresAt, new Date()),
          ),
      });

    if (!verificationRequest)
      throw new NotFoundException(
        'No verification request with this token was found',
      );

    if (isBefore(new Date(), verificationRequest.expiresAt)) {
      return verificationRequest;
    } else {
      this.delete(verificationRequest.id);
      throw new NotFoundException('Verification token has expired');
    }
  }

  async findByCodeAndUser(code: string, userId: string) {
    const verificationRequest =
      await this.db.query.credentialVerifications.findFirst({
        where: (credentialVerifications, { eq, and, gt }) =>
          and(
            eq(credentialVerifications.code, code),
            eq(credentialVerifications.userId, userId),
            gt(credentialVerifications.expiresAt, new Date()),
          ),
      });

    if (!verificationRequest)
      throw new NotFoundException(
        'No verification request with this token was found',
      );

    if (isBefore(new Date(), verificationRequest.expiresAt)) {
      return verificationRequest;
    } else {
      this.delete(verificationRequest.id);
      throw new NotFoundException('Verification token has expired');
    }
  }

  private async generateCode(userId: string, attempt = 0): Promise<string> {
    const code = generatePassword(6, {
      digits: true,
      lowerCaseAlphabets: false,
      upperCaseAlphabets: false,
      specialChars: false,
    });

    const codeExists = await this.db.query.credentialVerifications.findFirst({
      where: (credentialVerifications, { eq, and, gt }) =>
        and(
          eq(credentialVerifications.code, code),
          eq(credentialVerifications.userId, userId),
          gt(credentialVerifications.expiresAt, new Date()),
        ),
      columns: {
        id: true,
        expiresAt: true,
      },
    });

    if (codeExists && isBefore(new Date(), codeExists.expiresAt)) {
      if (attempt < 5) return this.generateCode(userId, attempt + 1);
      else throw new ConflictException('Code already exists');
    }
    return code;
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupExpiredTokens() {
    try {
      const result = await this.db
        .delete(credentialVerifications)
        .where(lt(credentialVerifications.expiresAt, new Date()));

      this.logger.log(
        `Cleaned up ${result.rowCount} expired verification tokens`,
      );
    } catch (error) {
      this.logger.error(
        'Failed to clean up expired verification tokens',
        error,
      );
    }
  }

  async manualCleanupExpiredTokens() {
    const result = await this.db
      .delete(credentialVerifications)
      .where(lt(credentialVerifications.expiresAt, new Date()));

    return { deletedCount: result.rowCount };
  }
}
