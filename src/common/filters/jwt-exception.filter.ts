import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  UnauthorizedException,
} from '@nestjs/common';
import { JsonWebTokenError, TokenExpiredError } from '@nestjs/jwt';
import { Response } from 'express';

import { ErrorCode, ErrorMessages } from '../exceptions/error-codes';

@Catch(JsonWebTokenError, UnauthorizedException)
export class JwtExceptionFilter implements ExceptionFilter {
  catch(
    exception: JsonWebTokenError | UnauthorizedException,
    host: ArgumentsHost,
  ) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    let errorCode: ErrorCode;
    let message: string;

    if (exception instanceof TokenExpiredError) {
      errorCode = ErrorCode.TOKEN_EXPIRED;
      message = ErrorMessages[ErrorCode.TOKEN_EXPIRED];
    } else if (exception instanceof JsonWebTokenError) {
      errorCode = ErrorCode.INVALID_TOKEN;
      message = ErrorMessages[ErrorCode.INVALID_TOKEN];
    } else {
      errorCode = ErrorCode.UNAUTHORIZED;
      message = ErrorMessages[ErrorCode.UNAUTHORIZED];
    }

    const errorResponse = {
      statusCode: 401,
      message,
      error: 'Unauthorized',
      errorCode,
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    return response.status(401).json(errorResponse);
  }
}
