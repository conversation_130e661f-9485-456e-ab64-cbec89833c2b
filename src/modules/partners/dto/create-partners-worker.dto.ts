import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { COUNTRIES } from '@/common/constants/countries.constant';
import { CountriesZodEnum } from '@/common/enums';

export const createPartnersWorkerSchema = z.object({
  userData: z.object({
    firstName: z.string(),
    lastName: z.string(),
    phoneNumber: z
      .string()
      .min(8)
      .max(15)
      .refine((val) => COUNTRIES.some((c) => val.startsWith(c.call_code)), {
        message: 'Phone number must start with a valid country code',
      }),
    birthDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
      message: 'Invalid date string',
    }),
    email: z.string().email().toLowerCase(),
    gender: z.enum(['male', 'female']).optional(),
    countryOfResidence: CountriesZodEnum,
    citizenship: CountriesZodEnum,
    password: z.string().min(8).describe('Password (min 8 characters)'),
  }),
  workerData: z.object({
    profession: z.string().min(2).optional(),
    hourlyRate: z
      .string()
      .refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
        message: 'Hourly rate must be a positive numeric value',
      })
      .optional(),
    projectId: z.string().length(36).optional(),
  }),
});

export class CreatePartnersWorkerDto extends createZodDto(
  createPartnersWorkerSchema,
) {}
