import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Inject,
  Param,
  Patch,
  Post,
  UseGuards,
  forwardRef,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { CanManage } from '@/common/decorators/management.decorator';
import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  BadRequestErrorResponseDto,
  ForbiddenErrorResponseDto,
  PartnerNotFoundErrorResponseDto,
  UnauthorizedErrorResponseDto,
  ValidationErrorResponseDto,
  WorkerNotFoundErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import {
  ManagementGuard,
  ResourceType,
} from '@/common/guards/management.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { Forwarded } from '@/common/types';
import { hashPassword } from '@/common/utils/hash';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { ManagersService } from '../managers/managers.service';
import { ProjectsService } from '../projects/projects.service';
import { UsersService } from '../users/users.service';
import { WorkersService } from '../workers/workers.service';
import { CreatePartnersManagerDto } from './dto/create-partners-manager.dto';
import { CreatePartnersWorkerDto } from './dto/create-partners-worker.dto';
import { FireWorkerDto } from './dto/fire-worker.dto';
import { PartnerWithUserDto } from './dto/partner.dto';
import { UpdatePartnerDto } from './dto/update-partner.dto';
import { UpdatePartnersWorkerDto } from './dto/update-partners-worker.dto';
import { PartnersService } from './partners.service';

@ApiTags('Partners')
@ApiBearerAuth()
@Controller('partners')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PartnersController {
  constructor(
    private readonly partnersService: PartnersService,
    @Inject(forwardRef(() => WorkersService))
    private readonly workersService: Forwarded<WorkersService>,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: Forwarded<UsersService>,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
    @Inject(forwardRef(() => ProjectsService))
    private readonly projectsService: Forwarded<ProjectsService>,
  ) {}

  @Get('me')
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Get partner profile',
    description: 'Retrieve the profile of the authenticated partner',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Partner profile retrieved successfully',
    type: PartnerWithUserDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access this profile',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Partner not found',
    type: PartnerNotFoundErrorResponseDto,
  })
  findMe(@User() user: RequestUserType): Promise<PartnerWithUserDto> {
    return this.partnersService.findOneWithUser(user.entityId);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get partner by ID',
    description: "Retrieve a specific partner's information",
  })
  @ApiParam({
    name: 'id',
    description: 'Partner unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Partner information retrieved successfully',
    type: PartnerWithUserDto,
  })
  @ApiNotFoundResponse({
    description: 'Partner not found',
    type: PartnerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Access forbidden',
    type: ForbiddenErrorResponseDto,
  })
  findOne(@Param('id') id: string): Promise<PartnerWithUserDto> {
    return this.partnersService.findOneWithUser(id);
  }

  @Patch('me')
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Update partner profile',
    description: 'Update personal information for the authenticated partner',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Partner profile updated successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to update this profile',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Partner was not found',
    type: PartnerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error',
    type: ValidationErrorResponseDto,
  })
  updatePersonalInfo(
    @Body() updatePartnerDto: UpdatePartnerDto,
    @User() user: RequestUserType,
  ) {
    return this.partnersService.update(user.entityId, updatePartnerDto);
  }

  @Patch('me/workers/:workerId')
  @Roles(Role.Partner)
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Worker)
  @ApiOperation({
    summary: 'Update worker information',
    description: 'Update information for a worker managed by this partner',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker information updated successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to update this worker',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Worker not found',
    type: WorkerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error',
    type: ValidationErrorResponseDto,
  })
  updateWorkerInfo(
    @Param('workerId') workerId: string,
    @Body() updateWorkerDto: UpdatePartnersWorkerDto,
  ) {
    return this.workersService.update(workerId, updateWorkerDto);
  }

  @Patch('me/workers/:workerId/fire')
  @Roles(Role.Partner)
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Worker)
  @ApiOperation({
    summary: 'Fire worker',
    description: 'Terminate employment of a worker',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker employment terminated successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to fire this worker',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Worker not found',
    type: WorkerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error or worker already terminated',
    type: BadRequestErrorResponseDto,
  })
  fireWorker(
    @User() user: RequestUserType,
    @Param('workerId') workerId: string,
    @Body() fireWorkerDto: FireWorkerDto,
  ) {
    return this.workersService.endEmployment(
      workerId,
      'terminated',
      user.id,
      fireWorkerDto.endDate,
      fireWorkerDto.reason,
    );
  }

  @Post('me/workers')
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Create new worker',
    description: 'Create a new worker account under this partner',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Worker created successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to create workers',
    type: ForbiddenErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error or email already exists',
    type: ValidationErrorResponseDto,
  })
  async createWorker(
    @Body() createWorkerDto: CreatePartnersWorkerDto,
    @User() user: RequestUserType,
  ) {
    const createdBaseUser = await this.usersService.create({
      ...createWorkerDto.userData,
      role: 'worker',
      hashedPassword: await hashPassword(createWorkerDto.userData.password),
    });

    return this.workersService.create({
      ...createWorkerDto.workerData,
      userId: createdBaseUser.id,
      partnerId: user.entityId,
    });
  }

  @Post('me/managers')
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Create new manager',
    description: 'Create a new manager account under this partner',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Manager created successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to create managers',
    type: ForbiddenErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error or email already exists',
    type: ValidationErrorResponseDto,
  })
  async createManager(
    @Body()
    createManagerData: CreatePartnersManagerDto,
    @User() user: RequestUserType,
  ) {
    const { password, ...safeUserData } = createManagerData.userData;
    const createdBaseUser = await this.usersService.create({
      ...safeUserData,
      role: 'manager',
      hashedPassword: await hashPassword(password),
    });

    return this.managersService.create({
      userId: createdBaseUser.id,
      partnerId: user.entityId,
      permissionType: createManagerData.managerData
        .permissionType as ManagerPermissionType,
    });
  }

  @Patch('me/managers/:managerId/permission')
  @Roles(Role.Partner)
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Manager)
  @ApiOperation({
    summary: 'Update manager permission type',
    description: 'Change the permission type of a manager',
  })
  @ApiParam({
    name: 'managerId',
    description: 'Manager unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Manager permission updated successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to update manager permissions',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Manager not found',
    type: WorkerNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Validation error',
    type: ValidationErrorResponseDto,
  })
  updateManagerPermission(
    @Param('managerId') managerId: string,
    @Body() { permissionType }: { permissionType: ManagerPermissionType },
  ) {
    return this.managersService.updatePermissionType(managerId, permissionType);
  }
}
