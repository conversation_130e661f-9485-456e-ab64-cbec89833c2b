import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { COUNTRIES } from '@/common/constants/countries.constant';
import { CountriesZodEnum } from '@/common/enums';

export const createPartnersManagerSchema = z.object({
  userData: z.object({
    firstName: z.string(),
    lastName: z.string(),
    phoneNumber: z
      .string()
      .min(8)
      .max(15)
      .refine((val) => COUNTRIES.some((c) => val.startsWith(c.call_code)), {
        message: 'Phone number must start with a valid country code',
      }),
    birthDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
      message: 'Invalid date string',
    }),
    email: z.string().email().toLowerCase(),
    gender: z.enum(['male', 'female']).optional(),
    countryOfResidence: CountriesZodEnum,
    citizenship: CountriesZodEnum,
    password: z.string().min(8).describe('Password (min 8 characters)'),
  }),
  managerData: z.object({
    permissionType: z.enum(['all', 'project_manager']),
  }),
});

export class CreatePartnersManagerDto extends createZodDto(
  createPartnersManagerSchema,
) {}
