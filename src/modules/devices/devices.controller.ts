import {
  Body,
  Controller,
  HttpStatus,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { User } from '@/common/decorators/user.decorator';
import {
  BadRequestErrorResponseDto,
  DeviceNotFoundErrorResponseDto,
  ForbiddenErrorResponseDto,
  UnauthorizedErrorResponseDto,
  ValidationErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { DeviceNotFoundException, DeviceNotOwnedException } from '@/common/exceptions/custom-exceptions';

import { DevicesService } from './devices.service';
import { CreateDeviceDto } from './dto/create-device.dto';
import { DeviceRegistrationResponseDto } from './dto/device-registration.dto';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

@ApiTags('Devices')
@Controller('devices')
export class DevicesController {
  constructor(private readonly devicesService: DevicesService) {}

  @Post('register')
  @ApiOperation({
    summary: 'Register device',
    description: 'Register a new device or update existing FCM token',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Device registered successfully',
    type: DeviceRegistrationResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid device data',
    type: ValidationErrorResponseDto,
  })
  async registerDevice(
    @Body() deviceDto: CreateDeviceDto,
  ): Promise<DeviceRegistrationResponseDto> {
    const existingDevice = await this.devicesService.findByToken(
      deviceDto.token,
    );

    if (existingDevice) {
      const device = await this.devicesService.update(
        existingDevice.id,
        deviceDto,
      );

      return {
        id: device.id,
        token: device.token,
        platform: device.platform,
        name: device.name,
      };
    }

    const device = await this.devicesService.create(deviceDto);

    return {
      id: device.id,
      token: device.token,
      platform: device.platform,
      name: device.name,
    };
  }

  @Put(':id/token')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Update device FCM token',
    description: 'Update FCM token for existing device',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Device token updated successfully',
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Device not owned by current user',
    type: ForbiddenErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Device not found',
    type: DeviceNotFoundErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid token data',
    type: BadRequestErrorResponseDto,
  })
  async updateToken(
    @User() user: RequestUserType,
    @Param('id') deviceId: string,
    @Body('token') newToken: string,
  ) {
    const device = await this.devicesService.findOne(deviceId);

    if (!device) {
      throw new DeviceNotFoundException();
    }

    if (device.userId !== user.id) {
      throw new DeviceNotOwnedException();
    }

    const existingDeviceWithToken =
      await this.devicesService.findByToken(newToken);
    if (existingDeviceWithToken && existingDeviceWithToken.id !== deviceId) {
      await this.devicesService.update(
        existingDeviceWithToken.id,
        {
          isActive: false,
        },
        user.id,
      );
    }
  }
}
