import { Module, forwardRef } from '@nestjs/common';

import { ActivityHistoryController } from './activity-history.controller';
import { ActivityHistoryService } from './activity-history.service';
import { ActivityHistoryMigrationService } from './activity-history-migration.service';
import { ManagersModule } from '../managers/managers.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    forwardRef(() => ManagersModule),
    forwardRef(() => UsersModule),
  ],
  controllers: [ActivityHistoryController],
  providers: [ActivityHistoryService, ActivityHistoryMigrationService],
  exports: [ActivityHistoryService, ActivityHistoryMigrationService],
})
export class ActivityHistoryModule {}
