import { Module, forwardRef } from '@nestjs/common';

import { RegistrationRequestsController } from './registration-requests.controller';
import { RegistrationRequestsService } from './registration-requests.service';
import { ActivityHistoryModule } from '../activity-history/activity-history.module';
import { EmploymentHistoryModule } from '../employment-history/employment-history.module';
import { ManagersModule } from '../managers/managers.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { RegistrationCodesModule } from '../registration-codes/registration-codes.module';
import { UpdatesModule } from '../updates/updates.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => WorkersModule),
    NotificationsModule,
    forwardRef(() => EmploymentHistoryModule),
    forwardRef(() => ActivityHistoryModule),
    forwardRef(() => RegistrationCodesModule),
    forwardRef(() => UpdatesModule),
  ],
  exports: [RegistrationRequestsService],
  providers: [RegistrationRequestsService],
  controllers: [RegistrationRequestsController],
})
export class RegistrationRequestsModule {}
