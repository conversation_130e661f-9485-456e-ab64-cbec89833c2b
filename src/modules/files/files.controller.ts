import { extname } from 'path';

import {
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { Request } from 'express';

import { User } from '@/common/decorators/user.decorator';
import {
  BadRequestErrorResponseDto,
  FileNotFoundErrorResponseDto,
  ForbiddenErrorResponseDto,
  UnauthorizedErrorResponseDto,
  ValidationErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { UnsupportedFileFormatException } from '@/common/exceptions/custom-exceptions';

import { CreateFileResponseDto } from './dto/create-file-response.dto';
import { GetFileUrlResponseDto } from './dto/get-file-url-response.dto';
import { UploadFileParamsDto } from './dto/upload-file-params.dto';
import { FilesService } from './files.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

const allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf', '.txt'];

const fileFilter = (
  _req: Request,
  file: Express.Multer.File,
  callback: (error: Error | null, acceptFile: boolean) => void,
) => {
  const fileExtension = extname(file.originalname).toLowerCase();

  if (!allowedExtensions.includes(fileExtension)) {
    return callback(new UnsupportedFileFormatException(allowedExtensions), false);
  }

  callback(null, true);
};

@ApiTags('Files')
@ApiBearerAuth()
@Controller('files')
@UseGuards(JwtAuthGuard)
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @Post('upload')
  @ApiOperation({
    summary: 'Upload file',
    description:
      'Uploads a file to the storage service.  Supports JPG, JPEG, PNG, PDF, and TXT files.  Maximum file size is 48MB.',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'File to upload',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The file to upload.',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'File uploaded successfully',
    type: CreateFileResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to upload files',
    type: ForbiddenErrorResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid file type or size. Ensure the file is one of the allowed types (JPG, JPEG, PNG, PDF, TXT) and does not exceed 24MB.',
    type: BadRequestErrorResponseDto,
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        fileSize: 24 * 1024 * 1024,
      },
      fileFilter,
    }),
  )
  uploadFile(
    @User() user: RequestUserType,
    @UploadedFile() file: Express.Multer.File,
    @Query() params: UploadFileParamsDto,
  ): Promise<CreateFileResponseDto> {
    return this.filesService.uploadFile(file, params, user.id);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get file URL',
    description:
      'Get either a temporary or permanent URL for a file, depending on the file security settings',
  })
  @ApiParam({
    name: 'id',
    description: 'File unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'File URL',
    type: GetFileUrlResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'File not found',
    type: FileNotFoundErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient file permissions',
    type: ForbiddenErrorResponseDto,
  })
  getFile(
    @Param('id') id: string,
    @User() user: RequestUserType,
  ): Promise<GetFileUrlResponseDto> {
    return this.filesService.getFileUrl(id, user.id);
  }

  // @Get(':id/url')
  // @ApiOperation({
  //   summary: 'Get presigned file URL',
  //   description: 'Generates a presigned file URL that can last for an hour',
  // })
  // @ApiParam({
  //   name: 'id',
  //   description: 'File unique identifier',
  // })
  // @ApiResponse({
  //   status: HttpStatus.OK,
  //   description: 'URL generated successfully',
  // })
  // @ApiNotFoundResponse({ description: 'File not found' })
  // @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  // async generatePresignedUrl(@Param('id') id: string) {
  //   await this.filesService.generatePresignedUrl(id);
  // }

  // @Delete(':key')
  // @ApiOperation({
  //   summary: 'Delete file',
  //   description: 'Delete a file from storage service',
  // })
  // @ApiParam({
  //   name: 'key',
  //   description: 'File unique identifier',
  // })
  // @ApiResponse({
  //   status: HttpStatus.OK,
  //   description: 'File deleted successfully',
  // })
  // @ApiNotFoundResponse({ description: 'File not found' })
  // @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  // async deleteFile(@Param('key') key: string) {
  //   return this.filesService.deleteFile(key);
  // }
}
