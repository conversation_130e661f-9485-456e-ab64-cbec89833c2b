import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';

import { presenceValidations } from '@/modules/db/entities/presence-validation.entity';
import { z } from 'zod';

export const getPresenceValidationSchema = createSelectSchema(
  presenceValidations,
).omit({
  photoUrl: true,
});

export const getPresenceValidationCountSchema = z.object({
  pendingCount: z.number(),
  emptyCount: z.number(),
});

export class PresenceValidationCountDto extends createZodDto(
  getPresenceValidationCountSchema,
) {}

export class PresenceValidationDto extends createZodDto(
  getPresenceValidationSchema,
) {}
