import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { ZodValidationException } from 'nestjs-zod';

import { ErrorCode, ErrorMessages } from '../exceptions/error-codes';
import { StandardHttpException } from '../exceptions/custom-exceptions';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const message =
      exception instanceof HttpException
        ? exception.message
        : ErrorMessages[ErrorCode.INTERNAL_SERVER_ERROR];

    // Determine error code
    let errorCode: ErrorCode;
    if (exception instanceof StandardHttpException) {
      errorCode = exception.getErrorCode();
    } else if (exception instanceof ZodValidationException) {
      errorCode = ErrorCode.VALIDATION_ERROR;
    } else if (exception instanceof HttpException) {
      // Map common HTTP exceptions to error codes
      errorCode = this.mapHttpExceptionToErrorCode(exception, status);
    } else {
      errorCode = ErrorCode.INTERNAL_SERVER_ERROR;
    }

    // Get error name for the error field
    const errorName = this.getErrorName(status);

    if (!(exception instanceof HttpException)) {
      console.error('Internal server error', exception);
    }

    // Handle Zod validation errors
    let zodErrors: {
      [x: string]: string[] | undefined;
      [x: number]: string[] | undefined;
      [x: symbol]: string[] | undefined;
    } = {};
    if (exception instanceof ZodValidationException) {
      const error = exception.getZodError();
      zodErrors = error.flatten().fieldErrors;
    }

    const errorResponse = {
      statusCode: status,
      message,
      error: errorName,
      errorCode,
      timestamp: new Date().toISOString(),
      path: request.url,
      ...(Object.keys(zodErrors).length > 0 && { validationErrors: zodErrors }),
    };

    response.status(status).json(errorResponse);
  }

  private mapHttpExceptionToErrorCode(exception: HttpException, status: number): ErrorCode {
    const message = exception.message.toLowerCase();

    // Map based on status code and message content
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        if (message.includes('validation')) return ErrorCode.VALIDATION_ERROR;
        return ErrorCode.BAD_REQUEST;
      case HttpStatus.UNAUTHORIZED:
        if (message.includes('credentials')) return ErrorCode.INVALID_CREDENTIALS;
        if (message.includes('token')) return ErrorCode.INVALID_TOKEN;
        return ErrorCode.UNAUTHORIZED;
      case HttpStatus.FORBIDDEN:
        return ErrorCode.FORBIDDEN;
      case HttpStatus.NOT_FOUND:
        if (message.includes('user')) return ErrorCode.USER_NOT_FOUND;
        if (message.includes('worker')) return ErrorCode.WORKER_NOT_FOUND;
        if (message.includes('partner')) return ErrorCode.PARTNER_NOT_FOUND;
        if (message.includes('project')) return ErrorCode.PROJECT_NOT_FOUND;
        if (message.includes('manager')) return ErrorCode.MANAGER_NOT_FOUND;
        return ErrorCode.NOT_FOUND;
      case HttpStatus.CONFLICT:
        if (message.includes('email')) return ErrorCode.EMAIL_ALREADY_EXISTS;
        if (message.includes('tax')) return ErrorCode.TAX_NUMBER_ALREADY_EXISTS;
        return ErrorCode.CONFLICT;
      default:
        return ErrorCode.INTERNAL_SERVER_ERROR;
    }
  }

  private getErrorName(status: number): string {
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        return 'Bad Request';
      case HttpStatus.UNAUTHORIZED:
        return 'Unauthorized';
      case HttpStatus.FORBIDDEN:
        return 'Forbidden';
      case HttpStatus.NOT_FOUND:
        return 'Not Found';
      case HttpStatus.CONFLICT:
        return 'Conflict';
      case HttpStatus.INTERNAL_SERVER_ERROR:
        return 'Internal Server Error';
      default:
        return 'Error';
    }
  }
}
