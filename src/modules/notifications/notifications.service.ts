import { Injectable, NotFoundException } from '@nestjs/common';
import * as firebase from 'firebase-admin';

import { SendNotificationDto } from './dto/send-notification.dto';
import { DevicesService } from '../devices/devices.service';

@Injectable()
export class NotificationsService {
  constructor(private readonly deviceService: DevicesService) {}

  async send(notification: SendNotificationDto) {
    const devices = await this.deviceService.findAllByUserId(
      notification.receiverUserId,
    );
    console.log('Sending notification to devices', {
      notification,
      devices,
    });
    if (devices.length === 0) {
      console.error('No devices found');
    };
    const tokens = devices.map((device) => device.token);

    try {
      await firebase
        .messaging()
        .sendEachForMulticast({
          notification: {
            title: notification.title,
            body: notification.body,
          },
          tokens,
          data: {
            topic: notification.topic,
            ...notification.data,
          },
          apns: {
            headers: {
              'apns-priority': '10',
            },
            payload: {
              aps: {
                'content-available': 1,
                'mutable-content': 1,
              },
            },
          },
          android: {
            priority: 'high',
          },
        })
        .catch((error: any) => {
          console.error(error);
        });

      await this.deviceService.updateLastUsedAt(
        devices.map((device) => device.id),
      );
    } catch (error) {
      console.log(error);
      return error;
    }
  }
}
