import { Inject, Injectable } from '@nestjs/common';
import { eq } from 'drizzle-orm';

import { Database } from '../db/db.module';
import { employmentHistory } from '../db/entities/employment-history.entity';
import { activityHistory } from '../db/entities/activity-history.entity';

@Injectable()
export class ActivityHistoryMigrationService {
  constructor(@Inject('DB') private readonly db: Database) {}

  async migrateEmploymentHistoryToActivityHistory() {
    console.log('Starting migration of employment history to activity history...');
    
    // Get all employment history records
    const employmentRecords = await this.db.query.employmentHistory.findMany();
    
    console.log(`Found ${employmentRecords.length} employment history records to migrate`);
    
    let migratedCount = 0;
    let skippedCount = 0;
    
    for (const record of employmentRecords) {
      try {
        // Check if already migrated
        const existing = await this.db.query.activityHistory.findFirst({
          where: eq(activityHistory.id, record.id),
        });
        
        if (existing) {
          skippedCount++;
          continue;
        }
        
        // Map employment change to activity type
        let activityType: string;
        if (record.workerId) {
          switch (record.change) {
            case 'hire':
              activityType = 'worker_hired';
              break;
            case 'quit':
              activityType = 'worker_quit';
              break;
            case 'terminated':
              activityType = 'worker_terminated';
              break;
            case 'quit_notice':
              activityType = 'worker_quit_notice';
              break;
            case 'terminated_notice':
              activityType = 'worker_terminated_notice';
              break;
            default:
              console.warn(`Unknown worker employment change: ${record.change}`);
              continue;
          }
        } else if (record.managerId) {
          switch (record.change) {
            case 'hire':
              activityType = 'manager_hired';
              break;
            case 'quit':
              activityType = 'manager_quit';
              break;
            case 'terminated':
              activityType = 'manager_terminated';
              break;
            case 'quit_notice':
              activityType = 'manager_quit_notice';
              break;
            case 'terminated_notice':
              activityType = 'manager_terminated_notice';
              break;
            default:
              console.warn(`Unknown manager employment change: ${record.change}`);
              continue;
          }
        } else {
          console.warn(`Employment record ${record.id} has no worker or manager ID`);
          continue;
        }
        
        // Insert into activity history
        await this.db.insert(activityHistory).values({
          id: record.id,
          activityType: activityType as any,
          operationAuthorId: record.operationAuthorId,
          workerId: record.workerId,
          managerId: record.managerId,
          projectId: null, // Employment history doesn't have project info
          partnerId: record.partnerId,
          metadata: null, // No metadata for migrated records
          happenedAt: record.happenedAt,
        });
        
        migratedCount++;
      } catch (error) {
        console.error(`Error migrating employment record ${record.id}:`, error);
      }
    }
    
    console.log(`Migration completed. Migrated: ${migratedCount}, Skipped: ${skippedCount}`);
    
    return {
      total: employmentRecords.length,
      migrated: migratedCount,
      skipped: skippedCount,
    };
  }
}
