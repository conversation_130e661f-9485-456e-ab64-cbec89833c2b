import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { CanManage } from '@/common/decorators/management.decorator';
import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  BadRequestErrorResponseDto,
  ForbiddenErrorResponseDto,
  ProjectNotFoundErrorResponseDto,
  UnauthorizedErrorResponseDto,
  ValidationErrorResponseDto,
  WorkerNotFoundErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import {
  ManagementGuard,
  ResourceType,
} from '@/common/guards/management.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { DailyReportsService } from '../daily-reports/daily-reports.service';
import { DailyReportWithPhotosDto } from '../daily-reports/dto/daily-report.dto';
import { WorkerFilterParamsDto } from '../workers/dto/worker-filter-params.dto';
import { WorkersWithBasicUserAndReportDto } from '../workers/dto/worker.dto';
import { WorkersService } from '../workers/workers.service';
import { CreateProjectDto } from './dto/create-projects.dto';
import {
  ProjectWithManagerDto,
  ProjectWithTeamSizeDto,
  SetProjectManagerDto,
} from './dto/project.dto';
import { ProjectsFilterParamsDto } from './dto/projects-filter-params.dto';
import { UpdatePartnerProjectDto } from './dto/update-project.dto';
import { ProjectsService } from './projects.service';
import { ReportFilterParamsDto } from '../daily-reports/dto/daily-reports-filter-params.dto';

@ApiTags('Projects')
@ApiBearerAuth()
@Controller('projects')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.Partner, { type: Role.Manager })
export class ProjectsController {
  constructor(
    private readonly projectsService: ProjectsService,
    private readonly workersService: WorkersService,
    private readonly dailyReportsService: DailyReportsService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all projects',
    description: 'Retrieve all projects for the authenticated partner',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of projects retrieved successfully',
    type: [ProjectWithTeamSizeDto],
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access projects',
    type: ForbiddenErrorResponseDto,
  })
  findAll(
    @User() user: RequestUserType,
    @Query() filter?: ProjectsFilterParamsDto,
  ): Promise<ProjectWithTeamSizeDto[]> {
    return this.projectsService.findAll(user, filter);
  }

  @Get(':projectId')
  @ApiOperation({
    summary: 'Get project by ID',
    description: 'Retrieve a specific project',
  })
  @ApiParam({
    name: 'projectId',
    description: 'Project unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Project found successfully',
    type: ProjectWithManagerDto,
  })
  @ApiNotFoundResponse({
    description: 'Project not found',
    type: ProjectNotFoundErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access this project',
    type: ForbiddenErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Project)
  findOne(
    @Param('projectId') projectId: string,
  ): Promise<ProjectWithManagerDto> {
    return this.projectsService.findOne(projectId);
  }

  @Get(':projectId/workers')
  @ApiOperation({
    summary: 'Get project workers',
    description: 'Retrieve all workers assigned to a specific project',
  })
  @ApiParam({
    name: 'projectId',
    description: 'Project unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workers retrieved successfully',
    type: [WorkersWithBasicUserAndReportDto],
  })
  @ApiNotFoundResponse({ description: 'Project not found' })
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Project)
  findAllWorkersByProjectId(
    @Param('projectId') projectId: string,
    @Query() filterParams?: WorkerFilterParamsDto,
  ): Promise<WorkersWithBasicUserAndReportDto[]> {
    return this.workersService.findAllByProjectId(projectId, filterParams);
  }

  @Get(':projectId/daily-reports')
  @ApiOperation({
    summary: 'Get project daily reports',
    description: 'Retrieve all daily reports for a specific project',
  })
  @ApiParam({
    name: 'projectId',
    description: 'Project unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reports retrieved successfully',
    type: [DailyReportWithPhotosDto],
  })
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Project)
  findAllReportsByProjectId(
    @Param('projectId') projectId: string,
    @Query() filterParams: ReportFilterParamsDto,
  ): Promise<DailyReportWithPhotosDto[]> {
    return this.dailyReportsService.findAllByProjectId(projectId, filterParams);
  }

  @Get(':projectId/confirmed-time')
  @ApiOperation({
    summary: 'Get project confirmed time',
    description: 'Calculate total confirmed work hours for a project',
  })
  @ApiParam({
    name: 'projectId',
    description: 'Project unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Total confirmed hours calculated successfully',
    type: Number,
  })
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Project)
  async findConfirmedTime(
    @Param('projectId') projectId: string,
  ): Promise<number> {
    const reports =
      await this.dailyReportsService.findAllByProjectId(projectId);
    return reports.reduce(
      (acc, { approvedHours }) => acc + (Number(approvedHours) || 0),
      0,
    );
  }

  @Post()
  @ApiOperation({
    summary: 'Create project',
    description: 'Create a new project for the authenticated partner',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Project created successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data',
    type: BadRequestErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to create projects',
    type: ForbiddenErrorResponseDto,
  })
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: { permissionType: ManagerPermissionType.All },
  })
  createProject(
    @User() user: RequestUserType,
    @Body() createProjectDto: CreateProjectDto,
  ) {
    return this.projectsService.create(createProjectDto, user);
  }

  @Patch(':projectId')
  @ApiOperation({
    summary: 'Update project',
    description: 'Update an existing project details',
  })
  @ApiParam({
    name: 'projectId',
    description: 'Project unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Project updated successfully',
  })
  @ApiNotFoundResponse({
    description: 'Project was not found',
  })
  @UseGuards(ManagementGuard)
  @CanManage([ResourceType.Project])
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: { permissionType: ManagerPermissionType.All },
  })
  updateProject(
    @Param('projectId') projectId: string,
    @Body() updateProjectDto: UpdatePartnerProjectDto,
  ) {
    return this.projectsService.update(projectId, updateProjectDto);
  }

  @Delete(':projectId')
  @ApiOperation({
    summary: 'Delete project',
    description: 'Delete an existing project',
  })
  @ApiParam({
    name: 'projectId',
    description: 'Project unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Project deleted successfully',
  })
  @UseGuards(ManagementGuard)
  @CanManage([ResourceType.Project])
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: { permissionType: ManagerPermissionType.All },
  })
  deleteProject(@Param('projectId') projectId: string) {
    return this.projectsService.remove(projectId);
  }

  @Post(':projectId/workers/:workerId')
  @ApiOperation({
    summary: 'Assign worker to project',
    description: 'Assign a worker to a specific project',
  })
  @ApiParam({ name: 'projectId', description: 'Project unique identifier' })
  @ApiParam({ name: 'workerId', description: 'Worker unique identifier' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker assigned successfully',
  })
  @UseGuards(ManagementGuard)
  @CanManage([ResourceType.Worker, ResourceType.Project])
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: { permissionType: ManagerPermissionType.All },
  })
  assignWorkerToProject(
    @Param('workerId') workerId: string,
    @Param('projectId') projectId: string,
    @User() user: RequestUserType,
  ) {
    return this.workersService.assignToProject(workerId, projectId, user.id);
  }

  @Delete(':projectId/workers/:workerId')
  @ApiOperation({
    summary: 'Unassign worker from project',
    description: 'Remove a worker from a specific project',
  })
  @ApiParam({ name: 'projectId', description: 'Project unique identifier' })
  @ApiParam({ name: 'workerId', description: 'Worker unique identifier' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker unassigned successfully',
  })
  @UseGuards(ManagementGuard)
  @CanManage([ResourceType.Worker, ResourceType.Project])
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: { permissionType: ManagerPermissionType.All },
  })
  unassignWorkerFromProject(
    @Param('workerId') workerId: string,
    @Param('projectId') projectId: string,
    @User() user: RequestUserType,
  ) {
    return this.workersService.unassignFromProject(workerId, projectId, user.id);
  }

  @Put(':projectId/manager')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Set or replace project manager',
    description:
      'Set or replace the manager for a project. Only one manager can be assigned at a time.',
  })
  @ApiParam({ name: 'projectId', description: 'Project unique identifier' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Manager set successfully',
  })
  @UseGuards(ManagementGuard)
  @CanManage([ResourceType.Project])
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: { permissionType: ManagerPermissionType.All },
  })
  async setProjectManager(
    @Param('projectId') projectId: string,
    @Body() dto: SetProjectManagerDto,
    @User() user: RequestUserType,
  ) {
    await this.projectsService.assignManagerToProjectExclusive(
      projectId,
      dto.managerId,
      user.id,
    );
  }

  @Delete(':projectId/manager')
  @ApiOperation({
    summary: 'Remove project manager',
    description: 'Remove the manager from a project.',
  })
  @ApiParam({ name: 'projectId', description: 'Project unique identifier' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Manager removed successfully',
  })
  @UseGuards(ManagementGuard)
  @CanManage([ResourceType.Project])
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: { permissionType: ManagerPermissionType.All },
  })
  async removeProjectManager(
    @Param('projectId') projectId: string,
    @User() user: RequestUserType,
  ) {
    return this.projectsService.removeAllManagersFromProject(projectId, user.id);
  }
}
