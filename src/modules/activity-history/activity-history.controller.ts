import {
  Controller,
  Get,
  HttpStatus,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import {
  ForbiddenErrorResponseDto,
  UnauthorizedErrorResponseDto,
} from '@/common/dto/error-response.dto';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import { ActivityHistoryDto } from './dto/activity-history.dto';
import { ActivityHistoryFilterParamsDto } from './dto/activity-history-filter-params.dto';
import { ActivityHistoryService } from './activity-history.service';
import { ActivityHistoryMigrationService } from './activity-history-migration.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';

@ApiTags('Activity History')
@ApiBearerAuth()
@Controller('activity-history')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.Partner, {
  type: Role.Manager,
  settings: {
    permissionType: ManagerPermissionType.All,
  },
})
export class ActivityHistoryController {
  constructor(
    private readonly activityHistoryService: ActivityHistoryService,
    private readonly activityHistoryMigrationService: ActivityHistoryMigrationService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all activity history',
    description:
      'Retrieve all activity history for the authenticated partner or manager. Includes employment changes, project assignments, and other tracked activities.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Activity history retrieved successfully',
    type: [ActivityHistoryDto],
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
    type: ForbiddenErrorResponseDto,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['date', 'activityType'],
    description: 'Sort by date or activity type',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
  })
  @ApiQuery({
    name: 'activityType',
    required: false,
    description: 'Filter by specific activity type',
  })
  @ApiQuery({
    name: 'role',
    required: false,
    enum: ['worker', 'manager'],
    description: 'Filter by role',
  })
  @ApiQuery({
    name: 'projectId',
    required: false,
    description: 'Filter by project ID',
  })
  findAll(
    @User() user: RequestUserType,
    @Query() filterParams?: ActivityHistoryFilterParamsDto,
  ): Promise<ActivityHistoryDto[]> {
    return this.activityHistoryService.findAll(user, filterParams);
  }

  @Post('migrate')
  @ApiOperation({
    summary: 'Migrate employment history to activity history',
    description: 'One-time migration to populate activity history from existing employment history records.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Migration completed successfully',
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
    type: UnauthorizedErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Only partners can perform migration',
    type: ForbiddenErrorResponseDto,
  })
  @Roles(Role.Partner)
  async migrateEmploymentHistory() {
    return this.activityHistoryMigrationService.migrateEmploymentHistoryToActivityHistory();
  }
}
